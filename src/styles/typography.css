/* ===================================
   ABADDON PEST CONTROL - TYPOGRAPHY
   Modern Typography with Inter Font
   =================================== */

/* Typography styles - Variables are defined in variables.css */

/* Global Typography Reset */
* {
  font-family: var(--font-primary);
  letter-spacing: var(--letter-spacing-base);
}

/* Base Typography - body styles are in global.css */

/* Headings */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-base);
  margin: 1rem 0 var(--spacing-md) 0;
  color: var(--color-text-primary);
}

h1 {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  letter-spacing: var(--letter-spacing-tight);
  color: var(--color-primary-gold);
}

h2 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

h3 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
}

h4 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
}

h5 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
}

h6 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

/* Paragraphs */
p {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  letter-spacing: var(--letter-spacing-base);
  margin: 0 0 var(--spacing-md) 0;
}

/* Links */
a {
  font-family: var(--font-primary);
  letter-spacing: var(--letter-spacing-base);
  color: var(--color-primary-gold);
  text-decoration: none;
  transition: var(--transition-normal);
}

a:hover {
  color: var(--color-primary-gold-dark);
  text-decoration: underline;
}

/* Buttons */
button,
.btn {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-semibold);
  letter-spacing: var(--letter-spacing-base);
  text-transform: none;
}

/* Form Elements */
input,
textarea,
select {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  letter-spacing: var(--letter-spacing-base);
}

/* Lists */
ul,
ol {
  font-family: var(--font-primary);
  letter-spacing: var(--letter-spacing-base);
}

li {
  font-family: var(--font-primary);
  letter-spacing: var(--letter-spacing-base);
  line-height: var(--line-height-normal);
}

/* Small Text */
small {
  font-size: var(--font-size-sm);
  letter-spacing: var(--letter-spacing-base);
}

/* Strong/Bold Text */
strong,
b {
  font-weight: var(--font-weight-semibold);
  letter-spacing: var(--letter-spacing-base);
}

/* Emphasis */
em,
i {
  font-style: italic;
  letter-spacing: var(--letter-spacing-base);
}

/* Code */
code,
pre {
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  letter-spacing: var(--letter-spacing-tight);
}

/* Responsive Typography */
@media (max-width: 768px) {
  :root {
    --font-size-5xl: 2.5rem; /* 40px */
    --font-size-4xl: 2rem; /* 32px */
    --font-size-3xl: 1.5rem; /* 24px */
  }

  body {
    font-size: var(--font-size-sm);
  }
}

@media (max-width: 480px) {
  :root {
    --font-size-5xl: 2rem; /* 32px */
    --font-size-4xl: 1.75rem; /* 28px */
    --font-size-3xl: 1.25rem; /* 20px */
  }
}

/* Utility Classes */
.text-xs {
  font-size: var(--font-size-xs);
}
.text-sm {
  font-size: var(--font-size-sm);
}
.text-base {
  font-size: var(--font-size-base);
}
.text-lg {
  font-size: var(--font-size-lg);
}
.text-xl {
  font-size: var(--font-size-xl);
}
.text-2xl {
  font-size: var(--font-size-2xl);
}
.text-3xl {
  font-size: var(--font-size-3xl);
}
.text-4xl {
  font-size: var(--font-size-4xl);
}
.text-5xl {
  font-size: var(--font-size-5xl);
}

.font-light {
  font-weight: var(--font-weight-light);
}
.font-normal {
  font-weight: var(--font-weight-normal);
}
.font-medium {
  font-weight: var(--font-weight-medium);
}
.font-semibold {
  font-weight: var(--font-weight-semibold);
}
.font-bold {
  font-weight: var(--font-weight-bold);
}
.font-extrabold {
  font-weight: var(--font-weight-extrabold);
}

.tracking-tight {
  letter-spacing: var(--letter-spacing-tight);
}
.tracking-normal {
  letter-spacing: var(--letter-spacing-base);
}
.tracking-wide {
  letter-spacing: var(--letter-spacing-wide);
}
.tracking-wider {
  letter-spacing: var(--letter-spacing-wider);
}

.leading-tight {
  line-height: var(--line-height-tight);
}
.leading-normal {
  line-height: var(--line-height-normal);
}
.leading-relaxed {
  line-height: var(--line-height-relaxed);
}
