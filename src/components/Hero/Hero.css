.hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--color-text-light);
  background: var(--gradient-hero),
    url("../../assets/images/hero-bg.jpg") no-repeat center center/cover;
}

.hero::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* width: 300px;
  height: 300px; */
  width: 80%;
  height: 80%;
  background: url("../../assets/logo/abaddon-logo.png") no-repeat center
    center/contain;
  opacity: 1;
  z-index: 0;
  pointer-events: none;
}

.hero__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-bg-overlay);
  z-index: 1;
}

.hero__content {
  position: relative;
  z-index: 2;
}

.hero__title {
  /* Technique pour rendre invisible visuellement mais accessible aux lecteurs d'écran */
  position: absolute;
  left: -10000px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;

  /* Alternative: utiliser opacity: 0 si vous préférez garder l'espace */
  /* opacity: 0;
  font-family: var(--font-primary);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  letter-spacing: var(--letter-spacing-base);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-md);
  color: var(--color-primary-gold); */
}

.hero__subtitle {
  font-family: var(--font-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-normal);
  letter-spacing: var(--letter-spacing-base);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacing-lg);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 768px) {
  .hero__subtitle {
    font-size: var(--font-size-2xl);
  }
}
