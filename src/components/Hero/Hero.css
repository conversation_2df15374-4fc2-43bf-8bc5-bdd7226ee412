.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--color-text-light);
  background: var(--gradient-hero),
    url("../../assets/images/hero-bg.jpg") no-repeat center center/cover;
}

.hero::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* width: 300px;
  height: 300px; */
  width: 80%;
  height: 80%;
  background: url("../../assets/logo/abaddon-logo.png") no-repeat center
    center/contain;
  opacity: 0.5;
  z-index: 0;
  pointer-events: none;
}

.hero__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-bg-overlay);
  z-index: 1;
}

.hero__content {
  position: relative;
  z-index: 2;
}

.hero__title {
  /* Technique pour rendre invisible visuellement mais accessible aux lecteurs d'écran */
  position: absolute;
  left: -10000px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Séquence de logos animés */
.hero__logo-sequence {
  display: flex;
  flex-direction: column;
  align-items: center;
  /* gap: var(--spacing-sm); */
  margin-block: var(--spacing-lg);
  min-height: 200px;
  justify-content: center;
}

.hero__logo-item {
  width: 100% !important;
  max-width: 260px;
  height: auto;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s ease-in-out;
}

/* Animation séquentielle avec délais */
.hero__logo-item--1 {
  animation: fadeInUp 0.8s ease-in-out 0.5s forwards;
}

.hero__logo-item--2 {
  animation: fadeInUp 0.8s ease-in-out 1.5s forwards;
}

.hero__logo-item--3 {
  animation: fadeInUp 0.8s ease-in-out 2.5s forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero__subtitle {
  font-family: var(--font-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-normal);
  letter-spacing: var(--letter-spacing-base);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacing-lg);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.hero__services-list {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
  display: inline-block;
}

.hero__services-list li {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-sm);
  color: var(--color-text-light);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-xs);
}

.hero__services-list li:before {
  content: "";
  flex-shrink: 0;
}

.hero__services-list strong {
  color: var(--color-primary-gold);
  font-weight: var(--font-weight-bold);
}

@media (min-width: 768px) {
  .hero__subtitle {
    font-size: var(--font-size-2xl);
  }

  .hero__logo-sequence {
    min-height: 250px;
  }

  .hero__services-list li {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
  }
}

@media (min-width: 1200px) {
  .hero__logo-sequence {
    min-height: 300px;
  }

  .hero__services-list {
    max-width: 800px;
  }

  .hero__services-list li {
    font-size: var(--font-size-xl);
  }
}
