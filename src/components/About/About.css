.about__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
  align-items: center;
}

.about__image img {
  width: 100%;
  height: auto;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-heavy);
}

.about__content p {
  margin-bottom: var(--spacing-md);
}

@media (min-width: 1100px) {
  .about__grid {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
  }
}
