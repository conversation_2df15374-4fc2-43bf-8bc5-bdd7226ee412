.services {
  background-color: var(--color-bg-secondary);
}

.services__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
}

.services__card {
  background-color: var(--color-bg-primary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-light);
  transition: var(--transition-normal);
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.services__card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.services__card-icon {
  width: 80px;
  height: 80px;
  margin-bottom: var(--spacing-md);
}

.services__card-title {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-sm);
  color: var(--color-text-primary);
}

.services__card-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  flex-grow: 1;
  margin-bottom: var(--spacing-md);
}

.services__card-cta {
  margin-top: auto;
  padding-top: var(--spacing-sm);
}

/* Section title responsive styles */
.section-title {
  font-size: var(--font-size-3xl); /* Mobile: 30px au lieu de 36px */
}

@media (min-width: 768px) {
  .services__grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-title {
    font-size: var(
      --font-size-4xl
    ); /* Tablet et plus: retour à la taille normale */
  }
}

@media (min-width: 1100px) {
  .services__grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
