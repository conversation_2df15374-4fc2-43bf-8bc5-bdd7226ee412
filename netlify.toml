[build]
  # Directory to change to before starting a build
  base = "."

  # Directory that contains the deploy-ready HTML files and assets generated by the build
  publish = "build"

  # Default build command
  command = "npm run build:netlify"

[build.environment]
  # Node.js version
  NODE_VERSION = "18"

# Redirect rules for SPA (Single Page Application)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # Cache control for static assets
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "/index.html"
  [headers.values]
    # Don't cache the main HTML file
    Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Disable Netlify processing (React handles its own optimization)
[build.processing]
  skip_processing = true
