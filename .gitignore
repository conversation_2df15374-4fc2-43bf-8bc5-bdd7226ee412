# ===================================
# ABADDON PEST CONTROL - .gitignore
# React TypeScript + Netlify Project
# ===================================

# Dependencies
node_modules/
/.pnp
.pnp.js
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Testing
/coverage
*.lcov
.nyc_output
/cypress/videos
/cypress/screenshots

# Production builds
/build
/dist
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging
.env.secret

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output (if used)
.next
out

# Gatsby files (if used)
.cache/
# public (commented out - we need public folder for React)

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Netlify
.netlify/
netlify.toml.backup

# Vercel (if used)
.vercel

# Turbo (if used)
.turbo

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*~

# Image optimization cache
.image-cache/

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# Local development
.local
local.env

# Build tools
.webpack/
.rollup.cache/

# Bundle analyzer reports
bundle-report.html
stats.json
analyze.html

# Lighthouse reports
lighthouse-report.html
lighthouse-report.json

# Performance monitoring
.clinic/
profile-*

# Security
secrets.json
private-key.json

# Backup files
*.backup
*.bak

# Archive files
*.zip
*.tar.gz
*.rar

# ===================================
# ABADDON PEST CONTROL SPECIFIC
# ===================================

# Design and asset backups
assets-backup/
images-original/
design-files/
psd-files/
ai-files/

# Client and business data
client-data/
sensitive-info/
customer-database/
leads-export/

# Image processing
optimized-images/
compressed-assets/

# Documentation builds
docs/build/
docs/.vuepress/dist/

# Analytics and tracking
.analytics/
google-analytics-key.json

# Social media assets
social-media-content/
facebook-assets/
tiktok-videos/

# SEO and marketing
seo-reports/
keyword-research/
competitor-analysis/

# Legal and compliance
fda-documents/
licenses-backup/
legal-docs/

# Development notes
dev-notes/
todo-private.md
meeting-notes/

# Python files (legacy, if any)
venv/
venv_linux/
__pycache__/
.ruff_cache/
.pytest_cache/
*.pyc
*.pyo
*.pyd

# Database files (if any local dev)
*.db
*.sqlite
*.sqlite3

# Email templates backup
email-templates-backup/

# Deployment scripts (sensitive)
deploy-scripts-private/
server-configs/

# ===================================
# KEEP THESE FILES IN REPO
# ===================================
# !public/favicon.ico
# !public/logo192.png
# !public/logo512.png
# !src/assets/logo/
# !src/assets/images/
# !src/assets/icons/
