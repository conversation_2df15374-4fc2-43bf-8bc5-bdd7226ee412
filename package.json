{"name": "abaddon-pest-control", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint .", "type-check": "tsc --noEmit", "build:netlify": "npm run type-check && npm run build", "preview": "npm run build && npx serve -s build", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^27.5.2", "@types/node": "^16.18.96", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "typescript-eslint-parser": "^22.0.0"}}