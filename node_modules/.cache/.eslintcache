[{"/home/<USER>/Bureau/abaddon/src/index.tsx": "1", "/home/<USER>/Bureau/abaddon/src/App.tsx": "2", "/home/<USER>/Bureau/abaddon/src/components/Hero/Hero.tsx": "3", "/home/<USER>/Bureau/abaddon/src/components/Contact/Contact.tsx": "4", "/home/<USER>/Bureau/abaddon/src/components/Header/Header.tsx": "5", "/home/<USER>/Bureau/abaddon/src/components/Footer/Footer.tsx": "6", "/home/<USER>/Bureau/abaddon/src/components/About/About.tsx": "7", "/home/<USER>/Bureau/abaddon/src/components/Services/Services.tsx": "8"}, {"size": 286, "mtime": 1752146485559, "results": "9", "hashOfConfig": "10"}, {"size": 561, "mtime": 1752138813985, "results": "11", "hashOfConfig": "10"}, {"size": 2349, "mtime": 1752159976629, "results": "12", "hashOfConfig": "10"}, {"size": 7272, "mtime": 1752144789571, "results": "13", "hashOfConfig": "10"}, {"size": 1732, "mtime": 1752147521141, "results": "14", "hashOfConfig": "10"}, {"size": 4913, "mtime": 1752159613050, "results": "15", "hashOfConfig": "10"}, {"size": 3312, "mtime": 1752162264662, "results": "16", "hashOfConfig": "10"}, {"size": 4455, "mtime": 1752161737691, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sg7hd1", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Bureau/abaddon/src/index.tsx", [], [], "/home/<USER>/Bureau/abaddon/src/App.tsx", [], [], "/home/<USER>/Bureau/abaddon/src/components/Hero/Hero.tsx", [], [], "/home/<USER>/Bureau/abaddon/src/components/Contact/Contact.tsx", [], [], "/home/<USER>/Bureau/abaddon/src/components/Header/Header.tsx", [], [], "/home/<USER>/Bureau/abaddon/src/components/Footer/Footer.tsx", [], [], "/home/<USER>/Bureau/abaddon/src/components/About/About.tsx", [], [], "/home/<USER>/Bureau/abaddon/src/components/Services/Services.tsx", [], []]