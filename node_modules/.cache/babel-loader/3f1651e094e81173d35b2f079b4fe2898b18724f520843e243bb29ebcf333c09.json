{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Bureau/abaddon/src/components/Services/Services.tsx\";\nimport React from \"react\";\nimport \"./Services.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const services = [{\n  title: \"Termite Control & Treatment\",\n  description: \"FDA-licensed termite control specialists. Pre-construction & post-construction treatment, termite baiting systems, and comprehensive inspections. Protecting Cavite homes from subterranean termites.\",\n  icon: \"../../assets/icons/termite.svg\"\n}, {\n  title: \"Cockroach Extermination\",\n  description: \"Professional cockroach control for German, American, and Oriental species. IPM approach with targeted treatments for residential and commercial properties in Metro Manila.\",\n  icon: \"../../assets/icons/cockroach.svg\"\n}, {\n  title: \"Ant Control Services\",\n  description: \"Specialized ant control for fire ants, carpenter ants, and pharaoh ants. Effective solutions for ant infestations in Dasmariñas, Muntinlupa, and Parañaque areas.\",\n  icon: \"../../assets/icons/ant.svg\"\n}, {\n  title: \"Rodent Control & Prevention\",\n  description: \"Humane rodent control for house rats and field mice. Complete rodent management including exclusion, trapping, and prevention for homes and businesses.\",\n  icon: \"../../assets/icons/rodent.svg\"\n}, {\n  title: \"Mosquito Control\",\n  description: \"Year-round mosquito control services to protect against dengue and malaria vectors. Essential for Philippine tropical climate with targeted treatments and prevention.\",\n  icon: \"../../assets/icons/mosquito.svg\" // À créer\n}];\nconst Services = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"services\",\n    className: \"services section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title text-center\",\n        children: \"Professional Pest Control Services in Cavite & Metro Manila\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"services__intro text-center\",\n        children: \"FDA-licensed pest control operator (PCO) specializing in Integrated Pest Management (IPM). Serving Dasmari\\xF1as, Muntinlupa, Para\\xF1aque with comprehensive pest solutions.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"services__grid\",\n        children: services.map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"services__card\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: service.icon,\n            alt: `${service.title} icon`,\n            className: \"services__card-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"services__card-title\",\n            children: service.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"services__card-description\",\n            children: service.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"services__card-cta\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#contact\",\n              className: \"btn btn-outline\",\n              children: \"Get Free Quote\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"services__guarantee text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u2713 FDA Licensed PCO\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), \" |\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u2713 Free Inspection\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), \" |\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u2713 IPM Certified\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), \" |\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u2713 583+ Satisfied Customers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_c = Services;\nexport default Services;\nvar _c;\n$RefreshReg$(_c, \"Services\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "services", "title", "description", "icon", "Services", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "service", "index", "src", "alt", "href", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Bureau/abaddon/src/components/Services/Services.tsx"], "sourcesContent": ["import React from \"react\";\nimport \"./Services.css\";\n\nexport interface Service {\n  title: string;\n  description: string;\n  icon: string; // Path to the icon/image\n}\n\nexport const services: Service[] = [\n  {\n    title: \"Termite Control & Treatment\",\n    description:\n      \"FDA-licensed termite control specialists. Pre-construction & post-construction treatment, termite baiting systems, and comprehensive inspections. Protecting Cavite homes from subterranean termites.\",\n    icon: \"../../assets/icons/termite.svg\",\n  },\n  {\n    title: \"Cockroach Extermination\",\n    description:\n      \"Professional cockroach control for German, American, and Oriental species. IPM approach with targeted treatments for residential and commercial properties in Metro Manila.\",\n    icon: \"../../assets/icons/cockroach.svg\",\n  },\n  {\n    title: \"Ant Control Services\",\n    description:\n      \"Specialized ant control for fire ants, carpenter ants, and pharaoh ants. Effective solutions for ant infestations in Dasmariñas, Muntinlupa, and Parañaque areas.\",\n    icon: \"../../assets/icons/ant.svg\",\n  },\n  {\n    title: \"Rodent Control & Prevention\",\n    description:\n      \"Humane rodent control for house rats and field mice. Complete rodent management including exclusion, trapping, and prevention for homes and businesses.\",\n    icon: \"../../assets/icons/rodent.svg\",\n  },\n  {\n    title: \"Mosquito Control\",\n    description:\n      \"Year-round mosquito control services to protect against dengue and malaria vectors. Essential for Philippine tropical climate with targeted treatments and prevention.\",\n    icon: \"../../assets/icons/mosquito.svg\", // À créer\n  },\n];\n\nconst Services = () => {\n  return (\n    <section id=\"services\" className=\"services section\">\n      <div className=\"container\">\n        <h2 className=\"section-title text-center\">\n          Professional Pest Control Services in Cavite & Metro Manila\n        </h2>\n        <p className=\"services__intro text-center\">\n          FDA-licensed pest control operator (PCO) specializing in Integrated\n          Pest Management (IPM). Serving Dasmariñas, Muntinlupa, Parañaque with\n          comprehensive pest solutions.\n        </p>\n        <div className=\"services__grid\">\n          {services.map((service, index) => (\n            <div className=\"services__card\" key={index}>\n              <img\n                src={service.icon}\n                alt={`${service.title} icon`}\n                className=\"services__card-icon\"\n              />\n              <h3 className=\"services__card-title\">{service.title}</h3>\n              <p className=\"services__card-description\">\n                {service.description}\n              </p>\n              <div className=\"services__card-cta\">\n                <a href=\"#contact\" className=\"btn btn-outline\">\n                  Get Free Quote\n                </a>\n              </div>\n            </div>\n          ))}\n        </div>\n        <div className=\"services__guarantee text-center\">\n          <p>\n            <strong>✓ FDA Licensed PCO</strong> |{\" \"}\n            <strong>✓ Free Inspection</strong> |{\" \"}\n            <strong>✓ IPM Certified</strong> |{\" \"}\n            <strong>✓ 583+ Satisfied Customers</strong>\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Services;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQxB,OAAO,MAAMC,QAAmB,GAAG,CACjC;EACEC,KAAK,EAAE,6BAA6B;EACpCC,WAAW,EACT,uMAAuM;EACzMC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,yBAAyB;EAChCC,WAAW,EACT,6KAA6K;EAC/KC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EACT,mKAAmK;EACrKC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,6BAA6B;EACpCC,WAAW,EACT,yJAAyJ;EAC3JC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,kBAAkB;EACzBC,WAAW,EACT,wKAAwK;EAC1KC,IAAI,EAAE,iCAAiC,CAAE;AAC3C,CAAC,CACF;AAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACEL,OAAA;IAASM,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eACjDR,OAAA;MAAKO,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBR,OAAA;QAAIO,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLZ,OAAA;QAAGO,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAC;MAI3C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJZ,OAAA;QAAKO,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BP,QAAQ,CAACY,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3Bf,OAAA;UAAKO,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BR,OAAA;YACEgB,GAAG,EAAEF,OAAO,CAACV,IAAK;YAClBa,GAAG,EAAE,GAAGH,OAAO,CAACZ,KAAK,OAAQ;YAC7BK,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACFZ,OAAA;YAAIO,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEM,OAAO,CAACZ;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzDZ,OAAA;YAAGO,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACtCM,OAAO,CAACX;UAAW;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACJZ,OAAA;YAAKO,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjCR,OAAA;cAAGkB,IAAI,EAAC,UAAU;cAACX,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA,GAd6BG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAerC,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNZ,OAAA;QAAKO,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eAC9CR,OAAA;UAAAQ,QAAA,gBACER,OAAA;YAAAQ,QAAA,EAAQ;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,MAAE,EAAC,GAAG,eACzCZ,OAAA;YAAAQ,QAAA,EAAQ;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,MAAE,EAAC,GAAG,eACxCZ,OAAA;YAAAQ,QAAA,EAAQ;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,MAAE,EAAC,GAAG,eACtCZ,OAAA;YAAAQ,QAAA,EAAQ;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACO,EAAA,GA3CId,QAAQ;AA6Cd,eAAeA,QAAQ;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}