{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Bureau/abaddon/src/components/About/About.tsx\";\nimport React from \"react\";\nimport \"./About.css\";\nimport aboutImage from \"../../assets/images/about-us.jpg\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"about\",\n    className: \"about section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"about__grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"about__image\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: aboutImage,\n            alt: \"Abaddon Pest Control team working\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"about__content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title\",\n            children: \"About Abaddon Pest Control Services Inc.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"about__credentials\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"about__badge\",\n              children: \"FDA Licensed PCO\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"about__badge\",\n              children: \"License #: CCHUHSRR-RIVA-PCO-01-ER-732510.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"about__badge\",\n              children: \" Valid until Sept 2026\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Founded in Salitran, Dasmari\\xF1as, Cavite\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), \", Abaddon Pest Control Services Inc. is a\", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \" FDA-licensed Pest Control Operator (PCO)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), \" \", \"dedicated to protecting homes and businesses across Metro Manila. Our mission is to deliver effective, reliable, and environmentally responsible pest management solutions using scientific approaches.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"We specialize in \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Integrated Pest Management (IPM)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 32\n            }, this), \", a comprehensive approach that combines thorough inspections, targeted treatments, and preventative measures to ensure long-term pest control. Our team of experienced technicians serves\", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \" Dasmari\\xF1as, Muntinlupa, Para\\xF1aque\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), \" and surrounding areas with the highest level of service and customer satisfaction.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"about__highlights\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"about__highlight\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\uD83C\\uDFC6 583+ Happy Customers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Trusted by families and businesses across Cavite and Metro Manila\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"about__highlight\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\uD83D\\uDD2C IPM Certified Approach\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Scientific, environmentally conscious pest management methods\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"about__highlight\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\uD83C\\uDD93 FREE Inspection\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Complimentary pest assessment and treatment recommendations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"about__service-areas\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Service Areas:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Primary:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), \" Salitran, Dasmari\\xF1as, Cavite (Garden Grove Village area)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metro Manila:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this), \" Muntinlupa City, Para\\xF1aque City, Las Pi\\xF1as\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Coverage:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this), \" Residential & Commercial Properties\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "aboutImage", "jsxDEV", "_jsxDEV", "About", "id", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Bureau/abaddon/src/components/About/About.tsx"], "sourcesContent": ["import React from \"react\";\nimport \"./About.css\";\nimport aboutImage from \"../../assets/images/about-us.jpg\";\n\nconst About = () => {\n  return (\n    <section id=\"about\" className=\"about section\">\n      <div className=\"container\">\n        <div className=\"about__grid\">\n          <div className=\"about__image\">\n            <img src={aboutImage} alt=\"Abaddon Pest Control team working\" />\n          </div>\n          <div className=\"about__content\">\n            <h2 className=\"section-title\">\n              About Abaddon Pest Control Services Inc.\n            </h2>\n            <div className=\"about__credentials\">\n              <span className=\"about__badge\">FDA Licensed PCO</span>\n              <span className=\"about__badge\">\n                License #: CCHUHSRR-RIVA-PCO-01-ER-732510. \n              </span>\n              <span className=\"about__badge\"> Valid until Sept 2026</span>\n            </div>\n            <p>\n              <strong>Founded in Salitran, Dasmariñas, Cavite</strong>, Abaddon\n              Pest Control Services Inc. is a \n              <strong> FDA-licensed Pest Control Operator (PCO)</strong>{\" \"}\n              dedicated to protecting homes and businesses across Metro Manila.\n              Our mission is to deliver effective, reliable, and environmentally\n              responsible pest management solutions using scientific approaches.\n            </p>\n            <p>\n              We specialize in <strong>Integrated Pest Management (IPM)</strong>\n              , a comprehensive approach that combines thorough inspections,\n              targeted treatments, and preventative measures to ensure long-term\n              pest control. Our team of experienced technicians serves \n              <strong> Dasmariñas, Muntinlupa, Parañaque</strong> and surrounding\n              areas with the highest level of service and customer satisfaction.\n            </p>\n            <div className=\"about__highlights\">\n              <div className=\"about__highlight\">\n                <h4>🏆 583+ Happy Customers</h4>\n                <p>\n                  Trusted by families and businesses across Cavite and Metro\n                  Manila\n                </p>\n              </div>\n              <div className=\"about__highlight\">\n                <h4>🔬 IPM Certified Approach</h4>\n                <p>\n                  Scientific, environmentally conscious pest management methods\n                </p>\n              </div>\n              <div className=\"about__highlight\">\n                <h4>🆓 FREE Inspection</h4>\n                <p>\n                  Complimentary pest assessment and treatment recommendations\n                </p>\n              </div>\n            </div>\n            <div className=\"about__service-areas\">\n              <h4>Service Areas:</h4>\n              <p>\n                <strong>Primary:</strong> Salitran, Dasmariñas, Cavite (Garden\n                Grove Village area)\n              </p>\n              <p>\n                <strong>Metro Manila:</strong> Muntinlupa City, Parañaque City,\n                Las Piñas\n              </p>\n              <p>\n                <strong>Coverage:</strong> Residential & Commercial Properties\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,aAAa;AACpB,OAAOC,UAAU,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAClB,oBACED,OAAA;IAASE,EAAE,EAAC,OAAO;IAACC,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC3CJ,OAAA;MAAKG,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBJ,OAAA;QAAKG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BJ,OAAA;UAAKG,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BJ,OAAA;YAAKK,GAAG,EAAEP,UAAW;YAACQ,GAAG,EAAC;UAAmC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACNV,OAAA;UAAKG,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BJ,OAAA;YAAIG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE9B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLV,OAAA;YAAKG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCJ,OAAA;cAAMG,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDV,OAAA;cAAMG,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAE/B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPV,OAAA;cAAMG,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNV,OAAA;YAAAI,QAAA,gBACEJ,OAAA;cAAAI,QAAA,EAAQ;YAAuC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,6CAExD,eAAAV,OAAA;cAAAI,QAAA,EAAQ;YAAyC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EAAC,yMAIjE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJV,OAAA;YAAAI,QAAA,GAAG,mBACgB,eAAAJ,OAAA;cAAAI,QAAA,EAAQ;YAAgC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,8LAIlE,eAAAV,OAAA;cAAAI,QAAA,EAAQ;YAAkC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uFAErD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJV,OAAA;YAAKG,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCJ,OAAA;cAAKG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BJ,OAAA;gBAAAI,QAAA,EAAI;cAAuB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChCV,OAAA;gBAAAI,QAAA,EAAG;cAGH;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNV,OAAA;cAAKG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BJ,OAAA;gBAAAI,QAAA,EAAI;cAAyB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClCV,OAAA;gBAAAI,QAAA,EAAG;cAEH;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNV,OAAA;cAAKG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BJ,OAAA;gBAAAI,QAAA,EAAI;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BV,OAAA;gBAAAI,QAAA,EAAG;cAEH;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNV,OAAA;YAAKG,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCJ,OAAA;cAAAI,QAAA,EAAI;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBV,OAAA;cAAAI,QAAA,gBACEJ,OAAA;gBAAAI,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gEAE3B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJV,OAAA;cAAAI,QAAA,gBACEJ,OAAA;gBAAAI,QAAA,EAAQ;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,qDAEhC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJV,OAAA;cAAAI,QAAA,gBACEJ,OAAA;gBAAAI,QAAA,EAAQ;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,wCAC5B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACC,EAAA,GA3EIV,KAAK;AA6EX,eAAeA,KAAK;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}