{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Bureau/abaddon/src/components/Hero/Hero.tsx\";\nimport React from \"react\";\nimport \"./Hero.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"hero\",\n    className: \"hero\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero__overlay\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container hero__content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"hero__title\",\n        children: \"FDA-Licensed Pest Control Services Dasmari\\xF1as, Cavite\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"hero__subtitle\",\n        children: [\"Professional termite control, cockroach extermination, ant & rodent control in Metro Manila. Integrated Pest Management (IPM) approach with \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"FREE INSPECTION\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 16\n        }, this), \". Licensed PCO serving Dasmari\\xF1as, Muntinlupa, Para\\xF1aque since 2020.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero__cta-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#contact\",\n          className: \"btn btn-primary\",\n          children: \"Get FREE Inspection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"tel:+639171234567\",\n          className: \"btn btn-secondary\",\n          children: \"Call Now: +63 ************\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero__credentials\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"hero__badge\",\n          children: \"FDA Licensed PCO\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"hero__badge\",\n          children: \"583+ Happy Customers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"hero__badge\",\n          children: \"IPM Certified\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Hero", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Bureau/abaddon/src/components/Hero/Hero.tsx"], "sourcesContent": ["import React from \"react\";\nimport \"./Hero.css\";\n\nconst Hero = () => {\n  return (\n    <section id=\"hero\" className=\"hero\">\n      <div className=\"hero__overlay\"></div>\n      <div className=\"container hero__content\">\n        <h1 className=\"hero__title\">\n          FDA-Licensed Pest Control Services Dasmariñas, Cavite\n        </h1>\n        <p className=\"hero__subtitle\">\n          Professional termite control, cockroach extermination, ant & rodent\n          control in Metro Manila. Integrated Pest Management (IPM) approach\n          with <strong>FREE INSPECTION</strong>. Licensed PCO serving\n          Dasmariñas, Muntinlupa, Parañaque since 2020.\n        </p>\n        <div className=\"hero__cta-group\">\n          <a href=\"#contact\" className=\"btn btn-primary\">\n            Get FREE Inspection\n          </a>\n          <a href=\"tel:+639171234567\" className=\"btn btn-secondary\">\n            Call Now: +63 ************\n          </a>\n        </div>\n        <div className=\"hero__credentials\">\n          <span className=\"hero__badge\">FDA Licensed PCO</span>\n          <span className=\"hero__badge\">583+ Happy Customers</span>\n          <span className=\"hero__badge\">IPM Certified</span>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,oBACED,OAAA;IAASE,EAAE,EAAC,MAAM;IAACC,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACjCJ,OAAA;MAAKG,SAAS,EAAC;IAAe;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrCR,OAAA;MAAKG,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCJ,OAAA;QAAIG,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE5B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLR,OAAA;QAAGG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,GAAC,8IAGvB,eAAAJ,OAAA;UAAAI,QAAA,EAAQ;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,8EAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJR,OAAA;QAAKG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BJ,OAAA;UAAGS,IAAI,EAAC,UAAU;UAACN,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJR,OAAA;UAAGS,IAAI,EAAC,mBAAmB;UAACN,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAE1D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNR,OAAA;QAAKG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCJ,OAAA;UAAMG,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrDR,OAAA;UAAMG,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzDR,OAAA;UAAMG,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACE,EAAA,GA9BIT,IAAI;AAgCV,eAAeA,IAAI;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}