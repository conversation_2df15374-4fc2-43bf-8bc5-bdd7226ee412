{"ast": null, "code": "import React,{useState}from\"react\";import\"./Contact.css\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Contact=()=>{const[formData,setFormData]=useState({name:\"\",email:\"\",phone:\"\",message:\"\"});const handleChange=e=>{setFormData({...formData,[e.target.name]:e.target.value});};const handleSubmit=e=>{e.preventDefault();// Basic validation\nif(formData.name&&formData.email&&formData.message){console.log(\"Form submitted:\",formData);// Here you would typically send the data to a server\nalert(\"Thank you for your message! We will get back to you shortly.\");setFormData({name:\"\",email:\"\",phone:\"\",message:\"\"});}else{alert(\"Please fill out all required fields.\");}};return/*#__PURE__*/_jsx(\"section\",{id:\"contact\",className:\"contact section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"section-title text-center\",children:\"Contact Abaddon Pest Control - FREE Inspection\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"contact__intro text-center\",children:[\"Get your \",/*#__PURE__*/_jsx(\"strong\",{children:\"FREE pest inspection\"}),\" today! FDA-licensed pest control services in Dasmari\\xF1as, Cavite and Metro Manila. Call, WhatsApp, or Viber us now.\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact__grid\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"contact__form-container\",children:/*#__PURE__*/_jsxs(\"form\",{className:\"contact__form\",onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"name\",children:\"Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"name\",name:\"name\",value:formData.name,onChange:handleChange,required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"email\",children:\"Email\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",id:\"email\",name:\"email\",value:formData.email,onChange:handleChange,required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"phone\",children:\"Phone (Optional)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"tel\",id:\"phone\",name:\"phone\",value:formData.phone,onChange:handleChange})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"message\",children:\"Message\"}),/*#__PURE__*/_jsx(\"textarea\",{id:\"message\",name:\"message\",value:formData.message,onChange:handleChange,required:true})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"btn btn-primary\",children:\"Send Message\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact__info\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Contact Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact__business-info\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\uD83C\\uDFE2 Abaddon Pest Control Services Inc.\"}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"FDA License:\"}),\" CCHUHSRR-RIVA-PCO-01-ER-732510\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Classification:\"}),\" Commercial Application, Exterminator\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact__address\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\uD83D\\uDCCD Business Address\"}),/*#__PURE__*/_jsx(\"p\",{children:/*#__PURE__*/_jsx(\"strong\",{children:\"42 Paliparan Molino Road Salawag\"})}),/*#__PURE__*/_jsx(\"p\",{children:\"Dasmari\\xF1as City, Cavite 4114, Philippines\"}),/*#__PURE__*/_jsx(\"p\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"https://www.google.com/maps?q=14.344438363320867,120.9524173725775\",target:\"_blank\",rel:\"noopener noreferrer\",className:\"btn btn-outline\",children:\"\\uD83D\\uDCCD View on Google Maps\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact__phone\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\uD83D\\uDCDE Call or Text\"}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Mobile:\"}),\" \",/*#__PURE__*/_jsx(\"a\",{href:\"tel:+639175842100\",children:\"+63 ************\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact__digital\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\uD83D\\uDCAC Digital Contact\"}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"WhatsApp:\"}),\" \",/*#__PURE__*/_jsx(\"a\",{href:\"https://wa.me/639175842100\",target:\"_blank\",rel:\"noopener noreferrer\",children:\"+63 ************\"})]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Viber:\"}),\" \",/*#__PURE__*/_jsx(\"a\",{href:\"viber://chat?number=639175842100\",target:\"_blank\",rel:\"noopener noreferrer\",children:\"+63 ************\"})]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Email:\"}),\" \",/*#__PURE__*/_jsx(\"a\",{href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact__social-links\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\uD83C\\uDF10 Follow Us\"}),/*#__PURE__*/_jsx(\"a\",{href:\"https://www.facebook.com/profile.php?id=100063857540013\",target:\"_blank\",rel:\"noopener noreferrer\",children:\"Facebook (583+ Followers)\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact__service-areas\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\uD83D\\uDDFA\\uFE0F Service Areas\"}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Primary:\"}),\" Dasmari\\xF1as City, Cavite\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Metro Manila:\"}),\" Muntinlupa, Para\\xF1aque, Las Pi\\xF1as\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Coverage:\"}),\" Residential & Commercial\"]})]})]})]})]})});};export default Contact;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}