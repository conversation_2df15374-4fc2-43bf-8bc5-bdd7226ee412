{"ast": null, "code": "/**\n * @license React\n * react.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nvar l = Symbol.for(\"react.element\"),\n  n = Symbol.for(\"react.portal\"),\n  p = Symbol.for(\"react.fragment\"),\n  q = Symbol.for(\"react.strict_mode\"),\n  r = Symbol.for(\"react.profiler\"),\n  t = Symbol.for(\"react.provider\"),\n  u = Symbol.for(\"react.context\"),\n  v = Symbol.for(\"react.forward_ref\"),\n  w = Symbol.for(\"react.suspense\"),\n  x = Symbol.for(\"react.memo\"),\n  y = Symbol.for(\"react.lazy\"),\n  z = Symbol.iterator;\nfunction A(a) {\n  if (null === a || \"object\" !== typeof a) return null;\n  a = z && a[z] || a[\"@@iterator\"];\n  return \"function\" === typeof a ? a : null;\n}\nvar B = {\n    isMounted: function () {\n      return !1;\n    },\n    enqueueForceUpdate: function () {},\n    enqueueReplaceState: function () {},\n    enqueueSetState: function () {}\n  },\n  C = Object.assign,\n  D = {};\nfunction E(a, b, e) {\n  this.props = a;\n  this.context = b;\n  this.refs = D;\n  this.updater = e || B;\n}\nE.prototype.isReactComponent = {};\nE.prototype.setState = function (a, b) {\n  if (\"object\" !== typeof a && \"function\" !== typeof a && null != a) throw Error(\"setState(...): takes an object of state variables to update or a function which returns an object of state variables.\");\n  this.updater.enqueueSetState(this, a, b, \"setState\");\n};\nE.prototype.forceUpdate = function (a) {\n  this.updater.enqueueForceUpdate(this, a, \"forceUpdate\");\n};\nfunction F() {}\nF.prototype = E.prototype;\nfunction G(a, b, e) {\n  this.props = a;\n  this.context = b;\n  this.refs = D;\n  this.updater = e || B;\n}\nvar H = G.prototype = new F();\nH.constructor = G;\nC(H, E.prototype);\nH.isPureReactComponent = !0;\nvar I = Array.isArray,\n  J = Object.prototype.hasOwnProperty,\n  K = {\n    current: null\n  },\n  L = {\n    key: !0,\n    ref: !0,\n    __self: !0,\n    __source: !0\n  };\nfunction M(a, b, e) {\n  var d,\n    c = {},\n    k = null,\n    h = null;\n  if (null != b) for (d in void 0 !== b.ref && (h = b.ref), void 0 !== b.key && (k = \"\" + b.key), b) J.call(b, d) && !L.hasOwnProperty(d) && (c[d] = b[d]);\n  var g = arguments.length - 2;\n  if (1 === g) c.children = e;else if (1 < g) {\n    for (var f = Array(g), m = 0; m < g; m++) f[m] = arguments[m + 2];\n    c.children = f;\n  }\n  if (a && a.defaultProps) for (d in g = a.defaultProps, g) void 0 === c[d] && (c[d] = g[d]);\n  return {\n    $$typeof: l,\n    type: a,\n    key: k,\n    ref: h,\n    props: c,\n    _owner: K.current\n  };\n}\nfunction N(a, b) {\n  return {\n    $$typeof: l,\n    type: a.type,\n    key: b,\n    ref: a.ref,\n    props: a.props,\n    _owner: a._owner\n  };\n}\nfunction O(a) {\n  return \"object\" === typeof a && null !== a && a.$$typeof === l;\n}\nfunction escape(a) {\n  var b = {\n    \"=\": \"=0\",\n    \":\": \"=2\"\n  };\n  return \"$\" + a.replace(/[=:]/g, function (a) {\n    return b[a];\n  });\n}\nvar P = /\\/+/g;\nfunction Q(a, b) {\n  return \"object\" === typeof a && null !== a && null != a.key ? escape(\"\" + a.key) : b.toString(36);\n}\nfunction R(a, b, e, d, c) {\n  var k = typeof a;\n  if (\"undefined\" === k || \"boolean\" === k) a = null;\n  var h = !1;\n  if (null === a) h = !0;else switch (k) {\n    case \"string\":\n    case \"number\":\n      h = !0;\n      break;\n    case \"object\":\n      switch (a.$$typeof) {\n        case l:\n        case n:\n          h = !0;\n      }\n  }\n  if (h) return h = a, c = c(h), a = \"\" === d ? \".\" + Q(h, 0) : d, I(c) ? (e = \"\", null != a && (e = a.replace(P, \"$&/\") + \"/\"), R(c, b, e, \"\", function (a) {\n    return a;\n  })) : null != c && (O(c) && (c = N(c, e + (!c.key || h && h.key === c.key ? \"\" : (\"\" + c.key).replace(P, \"$&/\") + \"/\") + a)), b.push(c)), 1;\n  h = 0;\n  d = \"\" === d ? \".\" : d + \":\";\n  if (I(a)) for (var g = 0; g < a.length; g++) {\n    k = a[g];\n    var f = d + Q(k, g);\n    h += R(k, b, e, f, c);\n  } else if (f = A(a), \"function\" === typeof f) for (a = f.call(a), g = 0; !(k = a.next()).done;) k = k.value, f = d + Q(k, g++), h += R(k, b, e, f, c);else if (\"object\" === k) throw b = String(a), Error(\"Objects are not valid as a React child (found: \" + (\"[object Object]\" === b ? \"object with keys {\" + Object.keys(a).join(\", \") + \"}\" : b) + \"). If you meant to render a collection of children, use an array instead.\");\n  return h;\n}\nfunction S(a, b, e) {\n  if (null == a) return a;\n  var d = [],\n    c = 0;\n  R(a, d, \"\", \"\", function (a) {\n    return b.call(e, a, c++);\n  });\n  return d;\n}\nfunction T(a) {\n  if (-1 === a._status) {\n    var b = a._result;\n    b = b();\n    b.then(function (b) {\n      if (0 === a._status || -1 === a._status) a._status = 1, a._result = b;\n    }, function (b) {\n      if (0 === a._status || -1 === a._status) a._status = 2, a._result = b;\n    });\n    -1 === a._status && (a._status = 0, a._result = b);\n  }\n  if (1 === a._status) return a._result.default;\n  throw a._result;\n}\nvar U = {\n    current: null\n  },\n  V = {\n    transition: null\n  },\n  W = {\n    ReactCurrentDispatcher: U,\n    ReactCurrentBatchConfig: V,\n    ReactCurrentOwner: K\n  };\nfunction X() {\n  throw Error(\"act(...) is not supported in production builds of React.\");\n}\nexports.Children = {\n  map: S,\n  forEach: function (a, b, e) {\n    S(a, function () {\n      b.apply(this, arguments);\n    }, e);\n  },\n  count: function (a) {\n    var b = 0;\n    S(a, function () {\n      b++;\n    });\n    return b;\n  },\n  toArray: function (a) {\n    return S(a, function (a) {\n      return a;\n    }) || [];\n  },\n  only: function (a) {\n    if (!O(a)) throw Error(\"React.Children.only expected to receive a single React element child.\");\n    return a;\n  }\n};\nexports.Component = E;\nexports.Fragment = p;\nexports.Profiler = r;\nexports.PureComponent = G;\nexports.StrictMode = q;\nexports.Suspense = w;\nexports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = W;\nexports.act = X;\nexports.cloneElement = function (a, b, e) {\n  if (null === a || void 0 === a) throw Error(\"React.cloneElement(...): The argument must be a React element, but you passed \" + a + \".\");\n  var d = C({}, a.props),\n    c = a.key,\n    k = a.ref,\n    h = a._owner;\n  if (null != b) {\n    void 0 !== b.ref && (k = b.ref, h = K.current);\n    void 0 !== b.key && (c = \"\" + b.key);\n    if (a.type && a.type.defaultProps) var g = a.type.defaultProps;\n    for (f in b) J.call(b, f) && !L.hasOwnProperty(f) && (d[f] = void 0 === b[f] && void 0 !== g ? g[f] : b[f]);\n  }\n  var f = arguments.length - 2;\n  if (1 === f) d.children = e;else if (1 < f) {\n    g = Array(f);\n    for (var m = 0; m < f; m++) g[m] = arguments[m + 2];\n    d.children = g;\n  }\n  return {\n    $$typeof: l,\n    type: a.type,\n    key: c,\n    ref: k,\n    props: d,\n    _owner: h\n  };\n};\nexports.createContext = function (a) {\n  a = {\n    $$typeof: u,\n    _currentValue: a,\n    _currentValue2: a,\n    _threadCount: 0,\n    Provider: null,\n    Consumer: null,\n    _defaultValue: null,\n    _globalName: null\n  };\n  a.Provider = {\n    $$typeof: t,\n    _context: a\n  };\n  return a.Consumer = a;\n};\nexports.createElement = M;\nexports.createFactory = function (a) {\n  var b = M.bind(null, a);\n  b.type = a;\n  return b;\n};\nexports.createRef = function () {\n  return {\n    current: null\n  };\n};\nexports.forwardRef = function (a) {\n  return {\n    $$typeof: v,\n    render: a\n  };\n};\nexports.isValidElement = O;\nexports.lazy = function (a) {\n  return {\n    $$typeof: y,\n    _payload: {\n      _status: -1,\n      _result: a\n    },\n    _init: T\n  };\n};\nexports.memo = function (a, b) {\n  return {\n    $$typeof: x,\n    type: a,\n    compare: void 0 === b ? null : b\n  };\n};\nexports.startTransition = function (a) {\n  var b = V.transition;\n  V.transition = {};\n  try {\n    a();\n  } finally {\n    V.transition = b;\n  }\n};\nexports.unstable_act = X;\nexports.useCallback = function (a, b) {\n  return U.current.useCallback(a, b);\n};\nexports.useContext = function (a) {\n  return U.current.useContext(a);\n};\nexports.useDebugValue = function () {};\nexports.useDeferredValue = function (a) {\n  return U.current.useDeferredValue(a);\n};\nexports.useEffect = function (a, b) {\n  return U.current.useEffect(a, b);\n};\nexports.useId = function () {\n  return U.current.useId();\n};\nexports.useImperativeHandle = function (a, b, e) {\n  return U.current.useImperativeHandle(a, b, e);\n};\nexports.useInsertionEffect = function (a, b) {\n  return U.current.useInsertionEffect(a, b);\n};\nexports.useLayoutEffect = function (a, b) {\n  return U.current.useLayoutEffect(a, b);\n};\nexports.useMemo = function (a, b) {\n  return U.current.useMemo(a, b);\n};\nexports.useReducer = function (a, b, e) {\n  return U.current.useReducer(a, b, e);\n};\nexports.useRef = function (a) {\n  return U.current.useRef(a);\n};\nexports.useState = function (a) {\n  return U.current.useState(a);\n};\nexports.useSyncExternalStore = function (a, b, e) {\n  return U.current.useSyncExternalStore(a, b, e);\n};\nexports.useTransition = function () {\n  return U.current.useTransition();\n};\nexports.version = \"18.3.1\";", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}