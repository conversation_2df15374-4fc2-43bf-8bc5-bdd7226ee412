{"ast": null, "code": "import React from\"react\";import\"./Services.css\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const services=[{title:\"Termite Control & Treatment\",description:\"FDA-licensed termite control specialists. Pre-construction & post-construction treatment, termite baiting systems, and comprehensive inspections. Protecting Cavite homes from subterranean termites.\",icon:\"/assets/icons/termite.svg\"},{title:\"Cockroach Extermination\",description:\"Professional cockroach control for German, American, and Oriental species. IPM approach with targeted treatments for residential and commercial properties in Metro Manila.\",icon:\"/assets/icons/cockroach.svg\"},{title:\"Ant Control Services\",description:\"Specialized ant control for fire ants, carpenter ants, and pharaoh ants. Effective solutions for ant infestations in Dasmariñas, Muntinlupa, and Parañaque areas.\",icon:\"/assets/icons/ant.svg\"},{title:\"Rodent Control & Prevention\",description:\"Humane rodent control for house rats and field mice. Complete rodent management including exclusion, trapping, and prevention for homes and businesses.\",icon:\"/assets/icons/rodent.svg\"},{title:\"Mosquito Control\",description:\"Year-round mosquito control services to protect against dengue and malaria vectors. Essential for Philippine tropical climate with targeted treatments and prevention.\",icon:\"/assets/icons/ant.svg\"// Using ant icon as placeholder\n}];const Services=()=>{return/*#__PURE__*/_jsx(\"section\",{id:\"services\",className:\"services section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"section-title text-center\",children:\"Professional Pest Control Services in Cavite & Metro Manila\"}),/*#__PURE__*/_jsx(\"p\",{className:\"services__intro text-center\",children:\"FDA-licensed pest control operator (PCO) specializing in Integrated Pest Management (IPM). Serving Dasmari\\xF1as, Muntinlupa, Para\\xF1aque with comprehensive pest solutions.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"services__grid\",children:services.map((service,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"services__card\",children:[/*#__PURE__*/_jsx(\"img\",{src:service.icon,alt:`${service.title} icon`,className:\"services__card-icon\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"services__card-title\",children:service.title}),/*#__PURE__*/_jsx(\"p\",{className:\"services__card-description\",children:service.description}),/*#__PURE__*/_jsx(\"div\",{className:\"services__card-cta\",children:/*#__PURE__*/_jsx(\"a\",{href:\"#contact\",className:\"btn btn-outline\",children:\"Get Free Quote\"})})]},index))}),/*#__PURE__*/_jsx(\"div\",{className:\"services__guarantee text-center\",children:/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u2713 FDA Licensed PCO\"}),\" |\",\" \",/*#__PURE__*/_jsx(\"strong\",{children:\"\\u2713 Free Inspection\"}),\" |\",\" \",/*#__PURE__*/_jsx(\"strong\",{children:\"\\u2713 IPM Certified\"}),\" |\",\" \",/*#__PURE__*/_jsx(\"strong\",{children:\"\\u2713 583+ Satisfied Customers\"})]})})]})});};export default Services;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}