{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Bureau/abaddon/src/components/Services/Services.tsx\";\nimport React from \"react\";\nimport \"./Services.css\";\n\n// Import des icônes SVG\nimport termiteIcon from \"../../assets/icons/termite.svg\";\nimport cockroachIcon from \"../../assets/icons/cockroach.svg\";\nimport antIcon from \"../../assets/icons/ant.svg\";\nimport rodentIcon from \"../../assets/icons/rodent.svg\";\nimport flyingInsectsIcon from \"../../assets/icons/flying-insects.svg\";\nimport bedBugsIcon from \"../../assets/icons/bed-bugs.svg\";\nimport fleaTickIcon from \"../../assets/icons/flea-tick.svg\";\nimport disinfectionIcon from \"../../assets/icons/disinfection.svg\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const services = [{\n  title: \"Termite Control & Treatment\",\n  description: \"FDA-licensed termite control specialists. Pre-construction & post-construction treatment, termite baiting systems, and comprehensive inspections. Protecting Cavite homes from subterranean termites.\",\n  icon: termiteIcon\n}, {\n  title: \"Cockroach Control\",\n  description: \"Professional cockroach control for German, American, and Oriental species. IPM approach with targeted treatments for residential and commercial properties in Metro Manila.\",\n  icon: cockroachIcon\n}, {\n  title: \"Ants Control\",\n  description: \"Specialized ant control for fire ants, carpenter ants, and pharaoh ants. Effective solutions for ant infestations in Dasmariñas, Muntinlupa, and Parañaque areas.\",\n  icon: antIcon\n}, {\n  title: \"Rats & Rodents Control\",\n  description: \"Humane rodent control for house rats and field mice. Complete rodent management including exclusion, trapping, and prevention for homes and businesses.\",\n  icon: rodentIcon\n}, {\n  title: \"Flying Insects Control\",\n  description: \"Comprehensive control for flies, mosquitoes, and bees. Year-round mosquito protection against dengue vectors, fly control for restaurants and homes, plus safe bee removal services.\",\n  icon: flyingInsectsIcon\n}, {\n  title: \"Bed Bugs Control\",\n  description: \"Specialized bed bug extermination using heat treatment and targeted pesticides. Complete elimination of bed bug infestations in homes, hotels, and dormitories.\",\n  icon: bedBugsIcon\n}, {\n  title: \"Flea & Tick Control\",\n  description: \"Pet-safe flea and tick control services. Comprehensive treatment for homes with pets, including yard treatments and indoor pest elimination.\",\n  icon: fleaTickIcon\n}, {\n  title: \"Disinfection Services\",\n  description: \"Professional disinfection and sanitization services. COVID-19 disinfection, general sanitization for homes, offices, and commercial establishments.\",\n  icon: disinfectionIcon\n}];\nconst Services = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"services\",\n    className: \"services section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title text-center\",\n        children: \"Professional Pest Control Services in Cavite & Metro Manila\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"services__intro\",\n        children: [\"FDA-licensed pest control operator (PCO) specializing in Integrated Pest Management (IPM).\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), \"Serving Dasmari\\xF1as, Muntinlupa, Para\\xF1aque with comprehensive pest solutions.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"services__grid\",\n        children: services.map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"services__card\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: service.icon,\n            alt: `${service.title} icon`,\n            className: \"services__card-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"services__card-title\",\n            children: service.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"services__card-description\",\n            children: service.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"services__card-cta\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#contact\",\n              className: \"btn btn-outline\",\n              children: \"Get Free Quote\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"services__guarantee text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u2713 FDA Licensed PCO\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), \" |\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u2713 Free Inspection\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), \" |\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u2713 IPM Certified\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), \" |\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u2713 583+ Satisfied Customers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_c = Services;\nexport default Services;\nvar _c;\n$RefreshReg$(_c, \"Services\");", "map": {"version": 3, "names": ["React", "termiteIcon", "cockroachIcon", "antIcon", "rodentIcon", "flyingInsectsIcon", "bedBugsIcon", "fleaTickIcon", "disinfectionIcon", "jsxDEV", "_jsxDEV", "services", "title", "description", "icon", "Services", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "service", "index", "src", "alt", "href", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Bureau/abaddon/src/components/Services/Services.tsx"], "sourcesContent": ["import React from \"react\";\nimport \"./Services.css\";\n\n// Import des icônes SVG\nimport termiteIcon from \"../../assets/icons/termite.svg\";\nimport cockroachIcon from \"../../assets/icons/cockroach.svg\";\nimport antIcon from \"../../assets/icons/ant.svg\";\nimport rodentIcon from \"../../assets/icons/rodent.svg\";\nimport flyingInsectsIcon from \"../../assets/icons/flying-insects.svg\";\nimport bedBugsIcon from \"../../assets/icons/bed-bugs.svg\";\nimport fleaTickIcon from \"../../assets/icons/flea-tick.svg\";\nimport disinfectionIcon from \"../../assets/icons/disinfection.svg\";\n\nexport interface Service {\n  title: string;\n  description: string;\n  icon: string; // Path to the icon/image\n}\n\nexport const services: Service[] = [\n  {\n    title: \"Termite Control & Treatment\",\n    description:\n      \"FDA-licensed termite control specialists. Pre-construction & post-construction treatment, termite baiting systems, and comprehensive inspections. Protecting Cavite homes from subterranean termites.\",\n    icon: termiteIcon,\n  },\n  {\n    title: \"Cockroach Control\",\n    description:\n      \"Professional cockroach control for German, American, and Oriental species. IPM approach with targeted treatments for residential and commercial properties in Metro Manila.\",\n    icon: cockroachIcon,\n  },\n  {\n    title: \"Ants Control\",\n    description:\n      \"Specialized ant control for fire ants, carpenter ants, and pharaoh ants. Effective solutions for ant infestations in Dasmariñas, Muntinlupa, and Parañaque areas.\",\n    icon: antIcon,\n  },\n  {\n    title: \"Rats & Rodents Control\",\n    description:\n      \"Humane rodent control for house rats and field mice. Complete rodent management including exclusion, trapping, and prevention for homes and businesses.\",\n    icon: rodentIcon,\n  },\n  {\n    title: \"Flying Insects Control\",\n    description:\n      \"Comprehensive control for flies, mosquitoes, and bees. Year-round mosquito protection against dengue vectors, fly control for restaurants and homes, plus safe bee removal services.\",\n    icon: flyingInsectsIcon,\n  },\n  {\n    title: \"Bed Bugs Control\",\n    description:\n      \"Specialized bed bug extermination using heat treatment and targeted pesticides. Complete elimination of bed bug infestations in homes, hotels, and dormitories.\",\n    icon: bedBugsIcon,\n  },\n  {\n    title: \"Flea & Tick Control\",\n    description:\n      \"Pet-safe flea and tick control services. Comprehensive treatment for homes with pets, including yard treatments and indoor pest elimination.\",\n    icon: fleaTickIcon,\n  },\n  {\n    title: \"Disinfection Services\",\n    description:\n      \"Professional disinfection and sanitization services. COVID-19 disinfection, general sanitization for homes, offices, and commercial establishments.\",\n    icon: disinfectionIcon,\n  },\n];\n\nconst Services = () => {\n  return (\n    <section id=\"services\" className=\"services section\">\n      <div className=\"container\">\n        <h2 className=\"section-title text-center\">\n          Professional Pest Control Services in Cavite & Metro Manila\n        </h2>\n        <p className=\"services__intro\">\n          FDA-licensed pest control operator (PCO) specializing in Integrated\n          Pest Management (IPM).\n          <br />\n          Serving Dasmariñas, Muntinlupa, Parañaque with comprehensive pest\n          solutions.\n        </p>\n        <div className=\"services__grid\">\n          {services.map((service, index) => (\n            <div className=\"services__card\" key={index}>\n              <img\n                src={service.icon}\n                alt={`${service.title} icon`}\n                className=\"services__card-icon\"\n              />\n              <h3 className=\"services__card-title\">{service.title}</h3>\n              <p className=\"services__card-description\">\n                {service.description}\n              </p>\n              <div className=\"services__card-cta\">\n                <a href=\"#contact\" className=\"btn btn-outline\">\n                  Get Free Quote\n                </a>\n              </div>\n            </div>\n          ))}\n        </div>\n        <div className=\"services__guarantee text-center\">\n          <p>\n            <strong>✓ FDA Licensed PCO</strong> |{\" \"}\n            <strong>✓ Free Inspection</strong> |{\" \"}\n            <strong>✓ IPM Certified</strong> |{\" \"}\n            <strong>✓ 583+ Satisfied Customers</strong>\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Services;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,gBAAgB;;AAEvB;AACA,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,OAAO,MAAM,4BAA4B;AAChD,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAOC,iBAAiB,MAAM,uCAAuC;AACrE,OAAOC,WAAW,MAAM,iCAAiC;AACzD,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,gBAAgB,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQnE,OAAO,MAAMC,QAAmB,GAAG,CACjC;EACEC,KAAK,EAAE,6BAA6B;EACpCC,WAAW,EACT,uMAAuM;EACzMC,IAAI,EAAEb;AACR,CAAC,EACD;EACEW,KAAK,EAAE,mBAAmB;EAC1BC,WAAW,EACT,6KAA6K;EAC/KC,IAAI,EAAEZ;AACR,CAAC,EACD;EACEU,KAAK,EAAE,cAAc;EACrBC,WAAW,EACT,mKAAmK;EACrKC,IAAI,EAAEX;AACR,CAAC,EACD;EACES,KAAK,EAAE,wBAAwB;EAC/BC,WAAW,EACT,yJAAyJ;EAC3JC,IAAI,EAAEV;AACR,CAAC,EACD;EACEQ,KAAK,EAAE,wBAAwB;EAC/BC,WAAW,EACT,sLAAsL;EACxLC,IAAI,EAAET;AACR,CAAC,EACD;EACEO,KAAK,EAAE,kBAAkB;EACzBC,WAAW,EACT,iKAAiK;EACnKC,IAAI,EAAER;AACR,CAAC,EACD;EACEM,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EACT,8IAA8I;EAChJC,IAAI,EAAEP;AACR,CAAC,EACD;EACEK,KAAK,EAAE,uBAAuB;EAC9BC,WAAW,EACT,qJAAqJ;EACvJC,IAAI,EAAEN;AACR,CAAC,CACF;AAED,MAAMO,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACEL,OAAA;IAASM,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eACjDR,OAAA;MAAKO,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBR,OAAA;QAAIO,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLZ,OAAA;QAAGO,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAAC,4FAG7B,eAAAR,OAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,sFAGR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJZ,OAAA;QAAKO,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BP,QAAQ,CAACY,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3Bf,OAAA;UAAKO,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BR,OAAA;YACEgB,GAAG,EAAEF,OAAO,CAACV,IAAK;YAClBa,GAAG,EAAE,GAAGH,OAAO,CAACZ,KAAK,OAAQ;YAC7BK,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACFZ,OAAA;YAAIO,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEM,OAAO,CAACZ;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzDZ,OAAA;YAAGO,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACtCM,OAAO,CAACX;UAAW;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACJZ,OAAA;YAAKO,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjCR,OAAA;cAAGkB,IAAI,EAAC,UAAU;cAACX,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA,GAd6BG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAerC,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNZ,OAAA;QAAKO,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eAC9CR,OAAA;UAAAQ,QAAA,gBACER,OAAA;YAAAQ,QAAA,EAAQ;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,MAAE,EAAC,GAAG,eACzCZ,OAAA;YAAAQ,QAAA,EAAQ;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,MAAE,EAAC,GAAG,eACxCZ,OAAA;YAAAQ,QAAA,EAAQ;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,MAAE,EAAC,GAAG,eACtCZ,OAAA;YAAAQ,QAAA,EAAQ;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACO,EAAA,GA7CId,QAAQ;AA+Cd,eAAeA,QAAQ;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}