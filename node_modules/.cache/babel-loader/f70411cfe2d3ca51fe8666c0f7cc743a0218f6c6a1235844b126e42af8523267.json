{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Bureau/abaddon/src/App.tsx\";\nimport React from 'react';\nimport Header from './components/Header/Header';\nimport Hero from './components/Hero/Hero';\nimport Services from './components/Services/Services';\nimport About from './components/About/About';\nimport Contact from './components/Contact/Contact';\nimport Footer from './components/Footer/Footer';\nimport './styles/global.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      children: [/*#__PURE__*/_jsxDEV(<PERSON>, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Services, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Header", "Hero", "Services", "About", "Contact", "Footer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Bureau/abaddon/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport Header from './components/Header/Header';\nimport Hero from './components/Hero/Hero';\nimport Services from './components/Services/Services';\nimport About from './components/About/About';\nimport Contact from './components/Contact/Contact';\nimport Footer from './components/Footer/Footer';\n\nimport './styles/global.css';\n\nfunction App() {\n  return (\n    <>\n      <Header />\n      <main>\n        <Hero />\n        <Services />\n        <About />\n        <Contact />\n      </main>\n      <Footer />\n    </>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,IAAI,MAAM,wBAAwB;AACzC,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,MAAM,MAAM,4BAA4B;AAE/C,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,SAASC,GAAGA,CAAA,EAAG;EACb,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACEJ,OAAA,CAACP,MAAM;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVR,OAAA;MAAAI,QAAA,gBACEJ,OAAA,CAACN,IAAI;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACRR,OAAA,CAACL,QAAQ;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACZR,OAAA,CAACJ,KAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACTR,OAAA,CAACH,OAAO;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eACPR,OAAA,CAACF,MAAM;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACV,CAAC;AAEP;AAACC,EAAA,GAbQN,GAAG;AAeZ,eAAeA,GAAG;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}