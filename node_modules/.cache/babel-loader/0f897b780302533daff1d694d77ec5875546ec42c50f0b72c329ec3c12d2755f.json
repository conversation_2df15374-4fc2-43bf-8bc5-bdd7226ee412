{"ast": null, "code": "import React,{useState}from\"react\";import\"./Header.css\";import logo from\"../../assets/logo/abaddon-logo.png\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const navLinks=[{name:\"Home\",href:\"#hero\"},{name:\"Services\",href:\"#services\"},{name:\"About Us\",href:\"#about\"},{name:\"Contact\",href:\"#contact\"}];const Header=()=>{const[isMobileMenuOpen,setMobileMenuOpen]=useState(false);const toggleMobileMenu=()=>{setMobileMenuOpen(!isMobileMenuOpen);};return/*#__PURE__*/_jsx(\"header\",{className:\"header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container header__container\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"#hero\",children:/*#__PURE__*/_jsx(\"img\",{src:logo,alt:\"Abaddon Pest Control Logo\",className:\"header__logo\"})}),/*#__PURE__*/_jsxs(\"nav\",{className:`header__nav ${isMobileMenuOpen?\"header__nav--open\":\"\"}`,children:[/*#__PURE__*/_jsx(\"ul\",{children:navLinks.map(link=>/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:link.href,children:link.name})},link.name))}),/*#__PURE__*/_jsx(\"a\",{href:\"#contact\",className:\"btn btn-primary header__cta--mobile\",children:\"FREE Inspection\"})]}),/*#__PURE__*/_jsx(\"a\",{href:\"#contact\",className:\"btn btn-primary header__cta--desktop\",children:\"FREE Inspection\"}),/*#__PURE__*/_jsxs(\"button\",{className:\"header__hamburger\",onClick:toggleMobileMenu,children:[/*#__PURE__*/_jsx(\"span\",{className:\"header__hamburger-line\"}),/*#__PURE__*/_jsx(\"span\",{className:\"header__hamburger-line\"}),/*#__PURE__*/_jsx(\"span\",{className:\"header__hamburger-line\"})]})]})});};export default Header;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}