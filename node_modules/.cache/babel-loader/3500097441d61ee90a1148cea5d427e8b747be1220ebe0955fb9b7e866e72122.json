{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Bureau/abaddon/src/components/About/About.tsx\";\nimport React from \"react\";\nimport \"./About.css\";\nimport aboutImage from \"../../assets/images/about-us.jpg\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"about\",\n    className: \"about section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"about__grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"about__image\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: aboutImage,\n            alt: \"Abaddon Pest Control team working\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"about__content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"section-title\",\n            children: \"About Abaddon Pest Control\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Founded in the heart of Cavite, Abaddon Pest Control is a fully licensed and certified pest control provider dedicated to protecting homes and businesses across the Philippines. Our mission is to deliver effective, reliable, and environmentally responsible pest management solutions.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"We specialize in Integrated Pest Management (IPM), a comprehensive approach that combines thorough inspections, targeted treatments, and preventative measures to ensure long-term pest control. Our team of experienced technicians is committed to providing the highest level of service and customer satisfaction.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "aboutImage", "jsxDEV", "_jsxDEV", "About", "id", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Bureau/abaddon/src/components/About/About.tsx"], "sourcesContent": ["import React from \"react\";\nimport \"./About.css\";\nimport aboutImage from \"../../assets/images/about-us.jpg\";\n\nconst About = () => {\n  return (\n    <section id=\"about\" className=\"about section\">\n      <div className=\"container\">\n        <div className=\"about__grid\">\n          <div className=\"about__image\">\n            <img src={aboutImage} alt=\"Abaddon Pest Control team working\" />\n          </div>\n          <div className=\"about__content\">\n            <h2 className=\"section-title\">About Abaddon Pest Control</h2>\n            <p>\n              Founded in the heart of Cavite, Abaddon Pest Control is a fully\n              licensed and certified pest control provider dedicated to\n              protecting homes and businesses across the Philippines. Our\n              mission is to deliver effective, reliable, and environmentally\n              responsible pest management solutions.\n            </p>\n            <p>\n              We specialize in Integrated Pest Management (IPM), a comprehensive\n              approach that combines thorough inspections, targeted treatments,\n              and preventative measures to ensure long-term pest control. Our\n              team of experienced technicians is committed to providing the\n              highest level of service and customer satisfaction.\n            </p>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,aAAa;AACpB,OAAOC,UAAU,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAClB,oBACED,OAAA;IAASE,EAAE,EAAC,OAAO;IAACC,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC3CJ,OAAA;MAAKG,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBJ,OAAA;QAAKG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BJ,OAAA;UAAKG,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BJ,OAAA;YAAKK,GAAG,EAAEP,UAAW;YAACQ,GAAG,EAAC;UAAmC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACNV,OAAA;UAAKG,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BJ,OAAA;YAAIG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA0B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DV,OAAA;YAAAI,QAAA,EAAG;UAMH;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJV,OAAA;YAAAI,QAAA,EAAG;UAMH;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACC,EAAA,GA7BIV,KAAK;AA+BX,eAAeA,KAAK;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}