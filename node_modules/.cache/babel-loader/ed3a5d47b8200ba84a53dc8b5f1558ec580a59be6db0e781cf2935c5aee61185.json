{"ast": null, "code": "import React from'react';import Header from'./components/Header/Header';import Hero from'./components/Hero/Hero';import Services from'./components/Services/Services';import About from'./components/About/About';import Contact from'./components/Contact/Contact';import Footer from'./components/Footer/Footer';import'./styles/global.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsxs(\"main\",{children:[/*#__PURE__*/_jsx(Hero,{}),/*#__PURE__*/_jsx(Services,{}),/*#__PURE__*/_jsx(About,{}),/*#__PURE__*/_jsx(Contact,{})]}),/*#__PURE__*/_jsx(Footer,{})]});}export default App;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}