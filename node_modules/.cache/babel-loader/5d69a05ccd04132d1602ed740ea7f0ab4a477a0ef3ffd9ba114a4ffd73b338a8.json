{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Bureau/abaddon/src/components/Services/Services.tsx\";\nimport React from 'react';\nimport './Services.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const services = [{\n  title: 'Termite Control',\n  description: 'Comprehensive termite inspection and treatment for residential and commercial properties.',\n  icon: '/assets/icons/termite.svg'\n}, {\n  title: 'Cockroach Extermination',\n  description: 'Effective solutions to eliminate cockroach infestations and prevent their return.',\n  icon: '/assets/icons/cockroach.svg'\n}, {\n  title: 'Ant Control',\n  description: 'Targeted treatments for all types of ant problems, from common ants to fire ants.',\n  icon: '/assets/icons/ant.svg'\n}, {\n  title: 'Rodent Control',\n  description: 'Humane and effective rodent control to protect your property from rats and mice.',\n  icon: '/assets/icons/rodent.svg'\n}];\nconst Services = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"services\",\n    className: \"services section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title text-center\",\n        children: \"Our Services\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"services__grid\",\n        children: services.map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"services__card\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: service.icon,\n            alt: `${service.title} icon`,\n            className: \"services__card-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"services__card-title\",\n            children: service.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"services__card-description\",\n            children: service.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_c = Services;\nexport default Services;\nvar _c;\n$RefreshReg$(_c, \"Services\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "services", "title", "description", "icon", "Services", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "service", "index", "src", "alt", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Bureau/abaddon/src/components/Services/Services.tsx"], "sourcesContent": ["import React from 'react';\nimport './Services.css';\n\nexport interface Service {\n  title: string;\n  description: string;\n  icon: string; // Path to the icon/image\n}\n\nexport const services: Service[] = [\n  {\n    title: 'Termite Control',\n    description: 'Comprehensive termite inspection and treatment for residential and commercial properties.',\n    icon: '/assets/icons/termite.svg',\n  },\n  {\n    title: 'Cockroach Extermination',\n    description: 'Effective solutions to eliminate cockroach infestations and prevent their return.',\n    icon: '/assets/icons/cockroach.svg',\n  },\n  {\n    title: 'Ant Control',\n    description: 'Targeted treatments for all types of ant problems, from common ants to fire ants.',\n    icon: '/assets/icons/ant.svg',\n  },\n  {\n    title: 'Rodent Control',\n    description: 'Humane and effective rodent control to protect your property from rats and mice.',\n    icon: '/assets/icons/rodent.svg',\n  },\n];\n\nconst Services = () => {\n  return (\n    <section id=\"services\" className=\"services section\">\n      <div className=\"container\">\n        <h2 className=\"section-title text-center\">Our Services</h2>\n        <div className=\"services__grid\">\n          {services.map((service, index) => (\n            <div className=\"services__card\" key={index}>\n              <img src={service.icon} alt={`${service.title} icon`} className=\"services__card-icon\" />\n              <h3 className=\"services__card-title\">{service.title}</h3>\n              <p className=\"services__card-description\">{service.description}</p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Services;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQxB,OAAO,MAAMC,QAAmB,GAAG,CACjC;EACEC,KAAK,EAAE,iBAAiB;EACxBC,WAAW,EAAE,2FAA2F;EACxGC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,yBAAyB;EAChCC,WAAW,EAAE,mFAAmF;EAChGC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,aAAa;EACpBC,WAAW,EAAE,mFAAmF;EAChGC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,KAAK,EAAE,gBAAgB;EACvBC,WAAW,EAAE,kFAAkF;EAC/FC,IAAI,EAAE;AACR,CAAC,CACF;AAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACEL,OAAA;IAASM,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eACjDR,OAAA;MAAKO,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBR,OAAA;QAAIO,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3DZ,OAAA;QAAKO,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BP,QAAQ,CAACY,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3Bf,OAAA;UAAKO,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BR,OAAA;YAAKgB,GAAG,EAAEF,OAAO,CAACV,IAAK;YAACa,GAAG,EAAE,GAAGH,OAAO,CAACZ,KAAK,OAAQ;YAACK,SAAS,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxFZ,OAAA;YAAIO,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEM,OAAO,CAACZ;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzDZ,OAAA;YAAGO,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAEM,OAAO,CAACX;UAAW;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GAHhCG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIrC,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACM,EAAA,GAjBIb,QAAQ;AAmBd,eAAeA,QAAQ;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}