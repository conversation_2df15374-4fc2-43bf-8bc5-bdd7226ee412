{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Bureau/abaddon/src/components/Header/Header.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport \"./Header.css\";\nimport logo from \"../../assets/logo/abaddon-logo.png\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const navLinks = [{\n  name: \"Home\",\n  href: \"#hero\"\n}, {\n  name: \"Services\",\n  href: \"#services\"\n}, {\n  name: \"About Us\",\n  href: \"#about\"\n}, {\n  name: \"Contact\",\n  href: \"#contact\"\n}];\nconst Header = () => {\n  _s();\n  const [isMobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const toggleMobileMenu = () => {\n    setMobileMenuOpen(!isMobileMenuOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container header__container\",\n      children: [/*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"#hero\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: logo,\n          alt: \"Abaddon Pest Control Logo\",\n          className: \"header__logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: `header__nav ${isMobileMenuOpen ? \"header__nav--open\" : \"\"}`,\n        children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n          children: navLinks.map(link => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: link.href,\n              children: link.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 17\n            }, this)\n          }, link.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#contact\",\n          className: \"btn btn-primary header__cta--mobile\",\n          children: \"Free Inspection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"#contact\",\n        className: \"btn btn-primary header__cta--desktop\",\n        children: \"Free Inspection\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"header__hamburger\",\n        onClick: toggleMobileMenu,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__hamburger-line\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__hamburger-line\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header__hamburger-line\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"TYD+qsXHexIO6cAO5FMW5RZpYBw=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "logo", "jsxDEV", "_jsxDEV", "navLinks", "name", "href", "Header", "_s", "isMobileMenuOpen", "setMobileMenuOpen", "toggleMobileMenu", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "link", "onClick", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Bureau/abaddon/src/components/Header/Header.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport \"./Header.css\";\nimport logo from \"../../assets/logo/abaddon-logo.png\";\n\nexport interface NavLink {\n  name: string;\n  href: string;\n}\n\nexport const navLinks: NavLink[] = [\n  { name: \"Home\", href: \"#hero\" },\n  { name: \"Services\", href: \"#services\" },\n  { name: \"About Us\", href: \"#about\" },\n  { name: \"Contact\", href: \"#contact\" },\n];\n\nconst Header = () => {\n  const [isMobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  const toggleMobileMenu = () => {\n    setMobileMenuOpen(!isMobileMenuOpen);\n  };\n\n  return (\n    <header className=\"header\">\n      <div className=\"container header__container\">\n        <a href=\"#hero\">\n          <img\n            src={logo}\n            alt=\"Abaddon Pest Control Logo\"\n            className=\"header__logo\"\n          />\n        </a>\n\n        <nav\n          className={`header__nav ${\n            isMobileMenuOpen ? \"header__nav--open\" : \"\"\n          }`}\n        >\n          <ul>\n            {navLinks.map((link) => (\n              <li key={link.name}>\n                <a href={link.href}>{link.name}</a>\n              </li>\n            ))}\n          </ul>\n          <a href=\"#contact\" className=\"btn btn-primary header__cta--mobile\">\n            Free Inspection\n          </a>\n        </nav>\n\n        <a href=\"#contact\" className=\"btn btn-primary header__cta--desktop\">\n          Free Inspection\n        </a>\n\n        <button className=\"header__hamburger\" onClick={toggleMobileMenu}>\n          <span className=\"header__hamburger-line\"></span>\n          <span className=\"header__hamburger-line\"></span>\n          <span className=\"header__hamburger-line\"></span>\n        </button>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,cAAc;AACrB,OAAOC,IAAI,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOtD,OAAO,MAAMC,QAAmB,GAAG,CACjC;EAAEC,IAAI,EAAE,MAAM;EAAEC,IAAI,EAAE;AAAQ,CAAC,EAC/B;EAAED,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE;AAAY,CAAC,EACvC;EAAED,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE;AAAS,CAAC,EACpC;EAAED,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAW,CAAC,CACtC;AAED,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,gBAAgB,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAMW,gBAAgB,GAAGA,CAAA,KAAM;IAC7BD,iBAAiB,CAAC,CAACD,gBAAgB,CAAC;EACtC,CAAC;EAED,oBACEN,OAAA;IAAQS,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBV,OAAA;MAAKS,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CV,OAAA;QAAGG,IAAI,EAAC,OAAO;QAAAO,QAAA,eACbV,OAAA;UACEW,GAAG,EAAEb,IAAK;UACVc,GAAG,EAAC,2BAA2B;UAC/BH,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEJhB,OAAA;QACES,SAAS,EAAE,eACTH,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,EAC1C;QAAAI,QAAA,gBAEHV,OAAA;UAAAU,QAAA,EACGT,QAAQ,CAACgB,GAAG,CAAEC,IAAI,iBACjBlB,OAAA;YAAAU,QAAA,eACEV,OAAA;cAAGG,IAAI,EAAEe,IAAI,CAACf,IAAK;cAAAO,QAAA,EAAEQ,IAAI,CAAChB;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC,GAD5BE,IAAI,CAAChB,IAAI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEd,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACLhB,OAAA;UAAGG,IAAI,EAAC,UAAU;UAACM,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAEnE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENhB,OAAA;QAAGG,IAAI,EAAC,UAAU;QAACM,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAEpE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJhB,OAAA;QAAQS,SAAS,EAAC,mBAAmB;QAACU,OAAO,EAAEX,gBAAiB;QAAAE,QAAA,gBAC9DV,OAAA;UAAMS,SAAS,EAAC;QAAwB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChDhB,OAAA;UAAMS,SAAS,EAAC;QAAwB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChDhB,OAAA;UAAMS,SAAS,EAAC;QAAwB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACX,EAAA,CA/CID,MAAM;AAAAgB,EAAA,GAANhB,MAAM;AAiDZ,eAAeA,MAAM;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}